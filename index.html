<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        *{
            /* padding: 0; */
            margin: 0;
            box-sizing: border-box;
        }
        body, html{
            width: 100%;
            height: 100vh;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body>
    
    <div id="main">
        <div class="container w-full h-screen">
            <div class="page1 flex h-full items-center justify-center">
                <div class="four-dise-image w-[50%] flex justify-center items-center h-screen">
                    <img class="h-[60%] object-cover" src="https://dice-game-aditya.vercel.app/images/dices.png" alt="">
                </div>
                
                <div class="four-dise-image w-[50%]">
                    <h2 class="text-[2vw]">SET IT</h2>
                    <button class="bg-black text-white p-3 cursor-pointer    rounded-[8px]">Set It</button>
                </div>
            </div>

            <div class="page2 w-full h-screen">
                <div class="top">
                    <div class="sc-dl">
                        <p>0</p>
                        <p>Total Score</p>
                    </div>
                    <div class="sc-nm">
                        <div class="nms">1</div>
                        <div class="nms">2</div>
                        <div class="nms">3</div>
                        <div class="nms">4</div>
                        <div class="nms">5</div>
                        <div class="nms">6</div>
                        <h2>Select Number</h2>
                    </div>
                </div>
                <div class="mid"></div>
                <div class="bottom"></div>
            </div>
            
        </div>
    </div>


    <script src="script.js"></script>
</body>
</html>