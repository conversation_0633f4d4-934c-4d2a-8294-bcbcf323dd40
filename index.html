<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dice Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .page1, .page2 {
            display: none;
        }
        .page1.active, .page2.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">

    <!-- Landing Page (Page 1) -->
    <div class="page1 active min-h-screen flex items-center justify-center">
        <div class="container mx-auto px-4 py-8">
            <div class="flex items-center justify-center min-h-screen">
                <!-- Left Side: Dice Image -->
                <div class="w-1/2 flex justify-center items-center">
                    <div class="relative">
                        <!-- Multiple Dice to create the scattered effect -->
                        <div class="relative w-80 h-80">
                            <!-- First Dice -->
                            <div class="absolute top-0 left-0 w-24 h-24 bg-white border-2 border-gray-300 rounded-lg shadow-lg transform rotate-12">
                                <div class="grid grid-cols-3 gap-1 p-2 h-full">
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                </div>
                            </div>

                            <!-- Second Dice -->
                            <div class="absolute top-8 right-0 w-24 h-24 bg-white border-2 border-gray-300 rounded-lg shadow-lg transform -rotate-12">
                                <div class="grid grid-cols-3 gap-1 p-2 h-full">
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                </div>
                            </div>

                            <!-- Third Dice -->
                            <div class="absolute bottom-0 left-8 w-24 h-24 bg-white border-2 border-gray-300 rounded-lg shadow-lg transform rotate-45">
                                <div class="grid grid-cols-3 gap-1 p-2 h-full">
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                    <div></div>
                                    <div></div>
                                    <div></div>
                                    <div class="bg-black rounded-full w-3 h-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Title and Button -->
                <div class="w-1/2 flex flex-col items-center justify-center">
                    <h1 class="text-8xl font-bold text-black mb-8 tracking-wider">DICE GAME</h1>
                    <button id="playNowBtn" class="px-8 py-4 bg-black text-white font-medium text-xl rounded hover:bg-gray-800 transition-colors">
                        Play Now
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Page (Page 2) -->
    <div class="page2 container mx-auto px-4 py-8">
        <!-- Top Section: Score and Number Selection -->
        <div class="flex justify-between items-start mb-16">
            <!-- Score Section -->
            <div class="text-left">
                <div class="text-8xl font-bold text-black mb-2">0</div>
                <div class="text-xl font-medium text-black">Total Score</div>
            </div>

            <div class="text-right">
                <div class="flex gap-4 mb-4">
                    <div class="w-16 h-16 border-2 border-black flex items-center justify-center text-xl font-semibold cursor-pointer hover:bg-black hover:text-white transition-colors">1</div>
                    <div class="w-16 h-16 border-2 border-black flex items-center justify-center text-xl font-semibold cursor-pointer hover:bg-black hover:text-white transition-colors">2</div>
                    <div class="w-16 h-16 border-2 border-black flex items-center justify-center text-xl font-semibold cursor-pointer hover:bg-black hover:text-white transition-colors">3</div>
                    <div class="w-16 h-16 border-2 border-black flex items-center justify-center text-xl font-semibold cursor-pointer hover:bg-black hover:text-white transition-colors">4</div>
                    <div class="w-16 h-16 border-2 border-black flex items-center justify-center text-xl font-semibold cursor-pointer hover:bg-black hover:text-white transition-colors">5</div>
                    <div class="w-16 h-16 border-2 border-black flex items-center justify-center text-xl font-semibold cursor-pointer hover:bg-black hover:text-white transition-colors">6</div>
                </div>
                <div class="text-xl font-medium text-black">Select Number</div>
            </div>
        </div>

        <!-- Center Section: Dice -->
        <div class="flex flex-col items-center mb-16">
            <!-- Dice Container -->
            <div class="w-48 h-48 bg-black rounded-2xl flex items-center justify-center mb-6 cursor-pointer hover:bg-gray-800 transition-colors shadow-2xl">
                <!-- Dice Dot -->
                <div class="w-8 h-8 bg-white rounded-full"></div>
            </div>

            <!-- Dice Text -->
            <div class="text-xl font-medium text-black">Click on Dice to Roll</div>
        </div>

        <!-- Bottom Section: Buttons -->
        <div class="flex flex-col items-center gap-4">
            <button class="px-12 py-3 border-2 border-black bg-white text-black font-medium rounded hover:bg-gray-100 transition-colors">
                Reset
            </button>
            <button class="px-12 py-3 bg-black text-white font-medium rounded hover:bg-gray-800 transition-colors">
                Show Rules
            </button>
        </div>
    </div>

    <script>
        // Page navigation functionality
        const page1 = document.querySelector('.page1');
        const page2 = document.querySelector('.page2');
        const playNowBtn = document.getElementById('playNowBtn');

        playNowBtn.addEventListener('click', function() {
            page1.classList.remove('active');
            page2.classList.add('active');
        });
    </script>
    <script src="script.js"></script>
</body>
</html>